<section class="content">
  <p-tabView [(activeIndex)]="selectedTabIndex">
    <p-tabPanel header="Logs de Usuarios">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h4 class="card-title">Filtros de búsqueda</h4>
              </div>
              <form [formGroup]="filtersForm" autocomplete="off" (ngSubmit)="formSubmitEventHandler()">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="login" class="form-label">Login</label>
                        <input type="text" class="form-control" id="login" formControlName="login">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="tipo" class="form-label">Tipo</label>
                        <input type="text" class="form-control" id="tipo" formControlName="tipo">
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-footer ">
                  <div class="col-md-4 d-inline-flex  justify-content-start">
                    <button pButton label="Nuevo" class="btn btn-block btn-info btn-sm w-auto"
                      (click)="btnNuevoUserLog()"><i class="fas fa-plus pr-1"></i>Nuevo</button>
                  </div>
                  <div class="col-md-8 d-inline-flex  justify-content-end">
                    <button type="submit" class="btn btn-block btn-success btn-sm w-auto"><i
                        class="fas fa-search pr-1"></i>Buscar</button>
                    <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                      (click)="clearFilters()"><i class="fas fa-eraser pr-1"></i>Limpiar</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-primary card-outline">
              <label>Registros: {{ totalRecords }}</label>
              <p-table [value]="userLogs" [paginator]="true" [rows]="20" [lazy]="true"
                (onLazyLoad)="loadUserLogs($event)" [totalRecords]="totalRecords" [loading]="loading">
                <ng-template pTemplate="header">
                  <tr>
                    <th class="text-center" style="width:10%">ID</th>
                    <th class="text-left" style="width:10%">Login</th>
                    <th class="text-left" style="width:10%">Tipo</th>
                    <th class="text-left" style="width:10%">Estado</th>
                    <th class="text-left" style="width:10%">Creado</th>
                    <th class="text-left" style="width:40%">Resultado</th>
                    <th class="text-center" style="width:10%">Acciones</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-userLog>
                  <tr>
                    <td class="text-center">{{ userLog.id }}</td>
                    <td class="text-left">{{ userLog.login }}</td>
                    <td class="text-left">{{ userLog.tipo }}</td>
                    <td class="text-left">{{ userLog.estado }}</td>
                    <td class="text-left">{{ userLog.creado }}</td>
                    <td class="text-left">{{ userLog.resultado }}</td>
                    <td class="text-center">
                      <div class="center">
                        <button (click)="btnVerUserLog(userLog)" class="btn btn-success btn-xs m-1" title="Ver">
                          <i class="fas fa-eye pr-1"></i>
                        </button>


                        <button (click)="btnEditarUserLog(userLog)" class="btn btn-info btn-xs m-1" title="Editar">
                          <i class="fas fa-pencil-alt pr-1"></i>
                        </button>
                        <button (click)="btnEliminarUserLog(userLog)" class="btn btn-danger btn-xs m-1" title="Baja">
                          <i class="fas fa-ban pr-1"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>

          </div>
        </div>
      </div>
    </p-tabPanel>
  </p-tabView>
</section>