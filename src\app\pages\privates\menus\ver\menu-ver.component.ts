import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Table } from "primeng/table";
import { MenuDataModel } from "src/app/commons/models/menu-data.model";
import { EventService } from "src/app/commons/services/event.service";
import { MenuService } from "src/app/commons/services/menu.service";
import { NotificationService } from "src/app/commons/services/notification.service";
import { BaseComponent } from "../../components/base.component";

@Component({
    selector: 'app-menu-ver',
    templateUrl: './menu-ver.component.html',
    styleUrls: ['./menu-ver.component.scss']
})
export class MenuVerComponent extends BaseComponent implements OnInit {

    menu: any;
    loading: boolean = true;

    constructor(
        private menuService: MenuService,
        private route: ActivatedRoute,
        notificationService: NotificationService,
        eventService: EventService
    ) {
        super(notificationService, eventService);
        this.menu = new MenuDataModel();
    }

    ngOnInit(): void {
        this.route.params.subscribe(params => {
            const menuId = params['id'];
            if (menuId) {
                this.loadMenu(menuId);
            }
        });
    }

    async loadMenu(menuId: number) {
        debugger;
        this.loading = true;
        try {
            const result = await this.menuService.getById(menuId);
            if (result.objeto) {
                this.menu = result.objeto;
            }
        } catch (error) {
            this.loading = false;
            this.notificationService.showError('Error al cargar el rol');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    btnVolver() {
        window.history.back();
    }

    clear(table: Table) {
        table.clear();
        // Encuentra el input dentro del caption de la tabla específica
        const input = table.el.nativeElement.querySelector('input[pInputText]');
        if (input) {
            (input as HTMLInputElement).value = '';
        }
    }
}