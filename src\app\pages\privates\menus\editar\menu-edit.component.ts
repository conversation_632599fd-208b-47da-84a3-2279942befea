import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Table } from 'primeng/table';
import { RoleDataModel } from 'src/app/commons/models/role-data.model';
import { RoleFullDataModel } from 'src/app/commons/models/role-full-data.model';
import { AccionService } from 'src/app/commons/services/accion.service';
import { EmpresaService } from 'src/app/commons/services/empresa.service';
import { EventService } from 'src/app/commons/services/event.service';
import { MenuService } from 'src/app/commons/services/menu.service';
import { NotificationService } from 'src/app/commons/services/notification.service';
import { RoleService } from 'src/app/commons/services/role.service';
import { UserService } from 'src/app/commons/services/user.service';
import { BaseComponent } from '../../components/base.component';
import { MenuDataModel } from 'src/app/commons/models/menu-data.model';

@Component({
    selector: 'app-menu-edit',
    templateUrl: './menu-edit.component.html',
    styleUrls: ['./menu-edit.component.scss']
})
export class MenuEditComponent extends BaseComponent implements OnInit {

    menu: any;
    editForm: FormGroup;
    maxNombre: number = 100;
    maxEstado: number = 100;
    nombreControl = new FormControl('', [Validators.maxLength(this.maxNombre), Validators.pattern('^[A-Z._ ]+$'), Validators.required]);
    estadoControl = new FormControl('', [Validators.maxLength(this.maxEstado), Validators.pattern(/^[A-Za-z0-9.,_\s]+$/), Validators.required]);
    loading: boolean = true;
    totalRecords: number = 0;
    nombreOriginal: any;

    // Variables para los picklists
    selectedTabIndex: number = 0;
    permisosAsignados: any = [];
    accionesAsignadas: any = [];
    menusAsignados: any = [];
    empresasAsignadas: any = [];
    usuariosAsignados: any = [];
    todosLosPermisos: any = [];
    todasLasAcciones: any = [];
    todosLosMenus: any = [];
    todasLasEmpresas: any = [];
    todosLosUsuarios: any = [];

    constructor(
        private roleService: RoleService,
        private permisoService: AccionService,
        private menuService: MenuService,
        private empresaService: EmpresaService,
        private usuarioService: UserService,
        private route: ActivatedRoute,
        notificationService: NotificationService,
        eventService: EventService
    ) {
        super(notificationService, eventService);
        this.menu = new MenuDataModel();
        this.editForm = new FormGroup({
            nombre: this.nombreControl,
            estado: this.estadoControl
        });
    }

    // *** CONTROL DE CARACTERES RESTANTES ***
    get letrasRestantesNombre(): number {
        return this.maxNombre - (this.editForm.get('nombre')?.value?.length || 0);
    }

    get letrasRestantesEstado(): number {
        return this.maxEstado - (this.editForm.get('estado')?.value?.length || 0);
    }

    // *** FIN CONTROL DE CARACTERES RESTANTES ***

    // Metodos de Angular 
    ngOnInit(): void {
        this.route.params.subscribe(params => {
            const menuId = params['id'];
            if (menuId) {
                this.loadRole(menuId);
            }
        });
    }

    // ******** FIN METODOS DE ANGULAR ********

    // Metodos propios de entidad
    async loadMenu(menuId: number) {
        this.loading = true;
        try {
            const result = await this.menuService.getById(menuId);
            if (result.objeto) {
                this.menu = result.objeto;
                this.nombreOriginal = this.menu.nombre;
                // Inicializa formulario del rol
                this.clearForm();
                // Obtenido el rol estos clear incializan los picklists
                this.clearPermisos();
                this.clearAcciones();
                this.clearMenus();
                this.clearEmpresas();
                this.clearUsuarios();
                // Vamos al primer TAB "Permisos NNSL"
                this.selectedTabIndex = 0;
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar el menu');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    btnVolver() {
        window.history.back();
    }

    clearForm() {
        this.editForm.reset();
        this.editForm.patchValue({
            nombre: this.menu.nombre,
            estado: this.menu.estado
        });
    }

    async formSubmitEventHandler(): Promise<void> {
        let debeProceder = true; // Bandera para controlar la ejecución

        if (this.editForm.invalid) {
            this.notificationService.showWarning('Por favor, revisa los campos antes de guardar.');
            debeProceder = false;
        } else if (this.nombreOriginal !== this.editForm.get('nombre')?.value) {
            const respuesta = await this.notificationService.showQuestion('Cambiar nombre del menu puede afectar el funcionamiento del sistema. ¿Desea continuar?');
            if (!respuesta) {
                this.editForm.get('nombre')?.setValue(this.nombreOriginal);
                debeProceder = false;
            }
        }

        if (debeProceder) { // Solo procede si la bandera es verdadera
            this.loading = true;
            this.editForm.updateValueAndValidity();
            const form = this.editForm.value;
            const request = new RoleFullDataModel();
            request.id = this.role.id;
            request.nombre = form.nombre;
            request.descripcion = form.descripcion;
            request.permisos = this.permisosAsignados;
            request.menues = this.role.menues;
            request.empresas = this.role.empresas;
            request.usuarios = this.role.usuarios;

            try {
                await this.roleService.update(request);
                // TODO revisar el update
                this.notificationService.show('Se ha guardado el registro.');
                this.loadRole(this.role.id);
            } catch (error) {
                this.notificationService.showWarning('Error al guardar el registro');
                console.error(error);
            } finally {
                this.loading = false;
            }
        }
    }

    onNombreChange(event: any) {
        const control = this.editForm.get('nombre');
        if (control) {
            const valor = event.target.value.toUpperCase();
            const regex = /^[A-Z._ ]+$/;

            if (regex.test(valor)) {
                control.setValue(valor, { emitEvent: true });
            } else {
                // Si hay caracteres inválidos, elimina el último carácter ingresado
                event.target.value = control.value || '';
            }
        }
    }

    validarTecla(event: KeyboardEvent) {
        const caracteresPermitidos = /^[A-Z._ ]$/; // Expresión regular para caracteres válidos
        const tecla = event.key.toUpperCase(); // Convertimos la tecla a mayúsculas

        // Permitir teclas de control como Backspace, Enter, etc.
        const teclasEspeciales = ['Backspace', 'Enter', 'ArrowLeft', 'ArrowRight', 'Delete'];

        if (!caracteresPermitidos.test(tecla) && !teclasEspeciales.includes(tecla)) {
            event.preventDefault(); // Bloquear la tecla si no está permitida
        }
    }

    clear(table: Table) {
        table.clear();
        // Encuentra el input dentro del caption de la tabla específica
        const input = table.el.nativeElement.querySelector('input[pInputText]');
        if (input) {
            (input as HTMLInputElement).value = '';
        }
    }
    // ******** FIN METODOS DE ENTIDAD ********

    // PICKLISTS
    // ********** PERMISOS **********
    async loadPermisosAsignados(roleId: number) {
        this.loading = true;
        try {
            const result = await this.permisoService.getByRole(roleId);
            if (result.objeto && result.objeto.length > 0) {
                this.permisosAsignados = result.objeto;
            } else {
                this.permisosAsignados = []
            }
            this.loadPermisosDisponibles();
            this.ordenarPermisos();
        } catch (error) {
            this.notificationService.showWarning('Error al cargar los permisos asignados');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    async loadPermisosDisponibles() {
        this.loading = true;
        const data = {
            "nombre": "",
            "descripcion": "",
            "size": 500,
            "page": 0,
            "sortBy": "nombre",
            "asc": true
        };
        try {
            const result = await this.permisoService.getAll(data);
            if (result.content && result.content.length > 0) {
                if (this.permisosAsignados && this.permisosAsignados.length > 0) {
                    // filtramos los asignados de los disponibles
                    const assignedPermisoIds = new Set(this.permisosAsignados.map((p: any) => p.id));
                    this.todosLosPermisos = result.content.filter(
                        (permiso: any) => !assignedPermisoIds.has(permiso.id)
                    );
                } else {
                    this.todosLosPermisos = result.content
                }
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar los permisos disponibles');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    private handleRestrictedPermissionsMove(movedItems: any[], restrictedItemNames: string[], toTarget: boolean): string[] {
        const preventedActionItemNames: string[] = [];

        for (const item of movedItems) {
            if (restrictedItemNames.includes(item.nombre)) {
                let shouldPreventMove = false;

                if (toTarget) { // Moviendo de 'todosLosPermisos' (Source) a 'permisosAsignados' (Target)
                    // Restricción: "Para rol distinto a ADMINISTRADOR no se pueden agregar los permisos..."
                    if (this.role.id !== 1) {
                        shouldPreventMove = true;
                    }
                } else { // Moviendo de 'permisosAsignados' (Target) a 'todosLosPermisos' (Source)
                    // Restricción: "Para rol ADMINISTRADOR no se pueden quitar los permisos..."
                    if (this.role.id === 1) {
                        shouldPreventMove = true;
                    }
                }

                if (shouldPreventMove) {
                    preventedActionItemNames.push(item.nombre);
                    // Revertir el movimiento que p-pickList ya hizo
                    if (toTarget) {
                        // Item fue movido de todosLosPermisos a permisosAsignados. Revertir.
                        this.permisosAsignados = this.permisosAsignados.filter((p: { id: any; }) => p.id !== item.id);
                        if (!this.todosLosPermisos.find((p: { id: any; }) => p.id === item.id)) {
                            this.todosLosPermisos.push(item);
                        }
                    } else {
                        // Item fue movido de permisosAsignados a todosLosPermisos. Revertir.
                        this.todosLosPermisos = this.todosLosPermisos.filter((p: { id: any; }) => p.id !== item.id);
                        if (!this.permisosAsignados.find((p: { id: any; }) => p.id === item.id)) {
                            this.permisosAsignados.push(item);
                        }
                    }
                }
            }
        }
        // Opcional: Re-ordenar arrays si el orden es importante después de push
        this.ordenarPermisos();
        return preventedActionItemNames;
    }

    private ordenarPermisos() {
        this.todosLosPermisos.sort((a: { nombre: string; }, b: { nombre: string; }) => a.nombre.localeCompare(b.nombre));
        this.permisosAsignados.sort((a: { nombre: string; }, b: { nombre: string; }) => a.nombre.localeCompare(b.nombre));
    }

    onPermisosMoveToSource(event: any) {
        // event.items contiene los elementos que se intentaron mover de target a source
        const preventedItems = this.handleRestrictedPermissionsMove(event.items, ['USER_WRITE', 'USER_READ'], false);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, los permisos: ${preventedItems.join(', ')} no se pueden quitar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para rol ADMINISTRADOR el permiso: ${preventedItems[0]} no se puede quitar.`);
        }
    }

    onPermisosMoveToTarget(event: any) {
        // event.items contiene los elementos que se intentaron mover de target a source
        const preventedItems = this.handleRestrictedPermissionsMove(event.items, ['USER_WRITE', 'USER_READ'], true);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para roles distintos a ADMINISTRADOR, los permisos: ${preventedItems.join(', ')} no se pueden agregar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para roles distintos a ADMINISTRADOR, el permiso: ${preventedItems[0]} no se puede agregar.`);
        }
    }

    clearPermisos() {
        this.loadPermisosAsignados(this.role.id);
        this.selectedTabIndex = 0;
    }

    async savePermisos() {

        this.loading = true;
        try {
            // Mapea los permisosAsignados para obtener solo sus IDs
            const idsPermisos = this.permisosAsignados.map((p: any) => ({ id: p.id }));
            // Mapea las accionesAsignadas para obtener solo sus IDs
            const idsAcciones = this.accionesAsignadas.map((a: any) => ({ id: a.id }));

            // Combina ambos arrays de IDs
            const todosLosIdsAsignados = [...idsPermisos, ...idsAcciones];

            // Crea el objeto request con la estructura deseada
            const requestData = {
                permisos: todosLosIdsAsignados
            };

            await this.permisoService.updateByRole(this.role.id, requestData);
            this.notificationService.show('Se ha guardado el registro.');
            this.loadRole(this.role.id);
            this.clearPermisos();
        } catch (error) {
            this.notificationService.showWarning('Error al guardar el registro');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    // ******** FIN PERMISOS ********

    // ********** ACCIONES **********
    async loadAccionesAsignadas(roleId: number) {
        this.loading = true;

        try {
            if (this.role.acciones && this.role.acciones.length > 0) {
                this.accionesAsignadas = this.role.acciones.filter((p: any) => p.nombre.includes('NNSL_') === false);
                this.loadAccionesDisponibles();
                this.ordenarAcciones();
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar las acciones asignadas');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    async loadAccionesDisponibles() {
        this.loading = true;
        try {
            const result = await this.permisoService.getAllLegacy();
            if (result.objeto && result.objeto.length > 0 && this.accionesAsignadas.length > 0) {

                // filtramos los asignados de los disponibles
                const assignedPermisoIds = new Set(this.accionesAsignadas.map((p: any) => p.id));
                this.todasLasAcciones = result.objeto.filter(
                    (permiso: any) => !assignedPermisoIds.has(permiso.id) && permiso.nombre.includes('NNSL_') === false
                )
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar las acciones disponibles');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    clearAcciones() {
        this.loadAccionesAsignadas(this.role.id);
        this.selectedTabIndex = 1;
    }

    async saveAcciones() {

        this.loading = true;
        try {
            // Mapea los permisossignados para obtener solo sus IDs
            const idsPermisos = this.permisosAsignados.map((p: any) => ({ id: p.id }));
            // Mapea las accionesAsignadas para obtener solo sus IDs
            const idsAcciones = this.accionesAsignadas.map((a: any) => ({ id: a.id }));

            // Combina ambos arrays de IDs
            const todosLosIdsAsignados = [...idsPermisos, ...idsAcciones];

            // Crea el objeto request con la estructura deseada
            const requestData = {
                permisos: todosLosIdsAsignados
            };

            await this.permisoService.updateByRole(this.role.id, requestData);
            this.notificationService.show('Se ha guardado el registro.');
            this.loadRole(this.role.id);
            this.clearAcciones(); // esto es lo distinto con savePermisos()
        } catch (error) {
            this.notificationService.showWarning('Error al guardar el registro');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    private handleRestrictedAccionesMove(movedItems: any[], restrictedItemNames: string[], toTarget: boolean): string[] {
        const preventedActionItemNames: string[] = [];

        for (const item of movedItems) {
            if (restrictedItemNames.includes(item.nombre)) {
                let shouldPreventMove = false;

                if (toTarget) { // Moviendo de 'todasLasAcciones' (Source) a 'accionesAsignadas' (Target)
                    // Asumimos restricción: "Para rol distinto a ADMINISTRADOR no se pueden agregar las acciones restringidas"
                    if (this.role.id !== 1) {
                        shouldPreventMove = true;
                    }
                } else { // Moviendo de 'accionesAsignadas' (Target) a 'todasLasAcciones' (Source)
                    // Restricción: "Para rol ADMINISTRADOR no se pueden quitar las acciones restringidas"
                    if (this.role.id === 1) {
                        shouldPreventMove = true;
                    }
                }

                if (shouldPreventMove) {
                    preventedActionItemNames.push(item.nombre);
                    // Revertir el movimiento que p-pickList ya hizo
                    if (toTarget) {
                        this.accionesAsignadas = this.accionesAsignadas.filter((a: { id: any; }) => a.id !== item.id);
                        if (!this.todasLasAcciones.find((a: { id: any; }) => a.id === item.id)) {
                            this.todasLasAcciones.push(item);
                        }
                    } else {
                        this.todasLasAcciones = this.todasLasAcciones.filter((a: { id: any; }) => a.id !== item.id);
                        if (!this.accionesAsignadas.find((a: { id: any; }) => a.id === item.id)) {
                            this.accionesAsignadas.push(item);
                        }
                    }
                }
            }
        }
        // Opcional: Re-ordenar arrays
        this.ordenarAcciones();
        return preventedActionItemNames;
    }

    private ordenarAcciones() {
        this.todasLasAcciones.sort((a: { nombre: string; }, b: { nombre: string; }) => a.nombre.localeCompare(b.nombre));
        this.accionesAsignadas.sort((a: { nombre: string; }, b: { nombre: string; }) => a.nombre.localeCompare(b.nombre));
    }

    onAccionesMoveToSource(event: any) {
        const preventedItems = this.handleRestrictedAccionesMove(event.items, ['NNSL_USER_WRITE', 'NNSL_USER_READ'], false);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, las acciones: ${preventedItems.join(', ')} no se pueden quitar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para rol ADMINISTRADOR no se puede quitar la acción: ${preventedItems[0]}.`);
        }
    }

    onAccionesMoveToTarget(event: any) {
        const preventedItems = this.handleRestrictedAccionesMove(event.items, ['NNSL_USER_WRITE', 'NNSL_USER_READ'], true); // Asumiendo mismos items restringidos


        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para roles distintos a ADMINISTRADOR, las acciones: ${preventedItems.join(', ')} no se pueden agregar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para roles distintos a ADMINISTRADOR, la acción: ${preventedItems[0]} no se puede agregar.`);
        }
    }

    // ******** FIN ACCIONES ********

    // ********** MENUS **********
    async loadMenusDisponibles() {
        this.loading = true;
        try {
            const result = await this.menuService.getAllLegacy();
            if (result.objeto && result.objeto.length > 0 && this.menusAsignados.length > 0) {
                // filtramos los asignados de los disponibles
                const assignedMenusIds = new Set(this.menusAsignados.map((m: any) => m.id));
                this.todosLosMenus = result.objeto.filter(
                    (menu: any) => !assignedMenusIds.has(menu.id)
                )
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar los menus disponibles');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    clearMenus() {
        this.menusAsignados = this.role.menues;
        this.loadMenusDisponibles();
        this.selectedTabIndex = 2;
    }

    onMenusMoveToSource(event: any) {
        // Asumimos que 'restrictedItemNames' son los nombres de los menús que no se pueden quitar para el rol ADMIN.
        // Si no hay nombres específicos, puedes pasar un array vacío y la lógica se basará solo en el rol.
        const preventedItems = this.handleRestrictedMenusMove(event.items, ['MENU_ADMIN_ESPECIFICO_1', 'MENU_ADMIN_ESPECIFICO_2']);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, los menús: ${preventedItems.join(', ')} no se pueden quitar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, el menú: ${preventedItems[0]} no se puede quitar.`);
        }
    }

    async saveMenus() {

        this.loading = true;
        try {
            // Mapea los menusAsignados para obtener solo sus IDs
            const todosLosIdsAsignados = this.menusAsignados.map((m: any) => ({ id: m.id }));

            // Crea el objeto request con la estructura deseada
            const requestData = {
                menus: todosLosIdsAsignados
            };

            await this.menuService.updateByRole(this.role.id, requestData);
            this.notificationService.show('Se ha guardado el registro.');
            this.loadRole(this.role.id);
            this.clearMenus();
        } catch (error) {
            this.notificationService.showWarning('Error al guardar el registro');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    private handleRestrictedMenusMove(movedItems: any[], restrictedItemNames: string[]): string[] {
        const preventedActionItemNames: string[] = [];

        for (const item of movedItems) {
            // Moviendo de 'menusAsignados' (Target) a 'todosLosMenus' (Source)
            // Restricción: "Para rol ADMINISTRADOR no se pueden quitar los menús restringidos"
            if (this.role.id === 1 && restrictedItemNames.includes(item.nombre)) {
                preventedActionItemNames.push(item.nombre);
                // Revertir el movimiento: quitar de todosLosMenus y añadir de nuevo a menusAsignados
                this.todosLosMenus = this.todosLosMenus.filter((m: { id: any; }) => m.id !== item.id);
                if (!this.menusAsignados.find((m: { id: any; }) => m.id === item.id)) {
                    this.menusAsignados.push(item);
                }
            }
        }
        // Opcional: Re-ordenar arrays
        // this.todosLosMenus.sort((a, b) => a.nombre.localeCompare(b.nombre));
        // this.menusAsignados.sort((a, b) => a.nombre.localeCompare(b.nombre));
        return preventedActionItemNames;
    }
    // ******** FIN MENUS ********

    // ********** EMPRESAS **********
    async loadEmpresasDisponibles() {
        this.loading = true;
        try {

            const data = {
                "activo": true,
                "sortBy": "codigo",
                "asc": true
            };

            const result = await this.empresaService.getAll(data);
            if (result && result.length > 0 && this.empresasAsignadas.length > 0) {
                // filtramos los asignados de los disponibles
                const assignedEmpresasIds = new Set(this.empresasAsignadas.map((m: any) => m.id));
                this.todasLasEmpresas = result.filter(
                    (empresa: any) => !assignedEmpresasIds.has(empresa.id)
                )
            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar las empresas disponibles');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    clearEmpresas() {
        this.empresasAsignadas = this.role.empresas;
        this.loadEmpresasDisponibles();
        this.selectedTabIndex = 3;
    }

    onEmpresaMoveToSource(event: any) {
        // Asumimos que 'restrictedItemNames' son los nombres/descripciones de las empresas que no se pueden quitar para el rol ADMIN.
        // Usaremos 'item.descripcion' para la comparación y el mensaje.
        const preventedItems = this.handleRestrictedEmpresasMove(event.items, ['EMPRESA_CRITICA_1', 'EMPRESA_CRITICA_2']);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, las empresas: ${preventedItems.join(', ')} no se pueden quitar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, la empresa: ${preventedItems[0]} no se puede quitar.`);
        }
    }

    async saveEmpresas() {

        this.loading = true;
        try {
            // Mapea las empresasAsignadas para obtener solo sus IDs
            const todosLosIdsAsignados = this.empresasAsignadas.map((e: any) => ({ id: e.id }));

            // Crea el objeto request con la estructura deseada
            const requestData = {
                empresas: todosLosIdsAsignados
            };

            await this.empresaService.updateByRole(this.role.id, requestData);
            this.notificationService.show('Se ha guardado el registro.');
            this.loadRole(this.role.id);
            this.clearEmpresas();
        } catch (error) {
            this.notificationService.showWarning('Error al guardar el registro');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    private handleRestrictedEmpresasMove(movedItems: any[], restrictedItemDescriptions: string[]): string[] {
        const preventedActionItemDescriptions: string[] = [];

        for (const item of movedItems) {
            // Moviendo de 'empresasAsignadas' (Target) a 'todasLasEmpresas' (Source)
            // Restricción: "Para rol ADMINISTRADOR no se pueden quitar las empresas restringidas"
            // Usamos item.descripcion para la comparación y el mensaje.
            if (this.role.id === 1 && restrictedItemDescriptions.includes(item.descripcion)) {
                preventedActionItemDescriptions.push(item.descripcion);
                // Revertir el movimiento
                this.todasLasEmpresas = this.todasLasEmpresas.filter((e: { id: any; }) => e.id !== item.id);
                if (!this.empresasAsignadas.find((e: { id: any; }) => e.id === item.id)) {
                    this.empresasAsignadas.push(item);
                }
            }
        }
        // Opcional: Re-ordenar arrays
        // this.todasLasEmpresas.sort((a, b) => a.descripcion.localeCompare(b.descripcion));
        // this.empresasAsignadas.sort((a, b) => a.descripcion.localeCompare(b.descripcion));
        return preventedActionItemDescriptions;
    }
    // ******** FIN EMPRESAS ********

    // ********** USUARIOS **********
    async loadUsuariosDisponibles() {

        this.loading = true;
        try {
            const result = await this.usuarioService.getAllFast();
            if (result.objeto && result.objeto.length > 0 && this.usuariosAsignados.length > 0) {
                // filtramos los asignados de los disponibles
                const assignedUsuariosIds = new Set(this.usuariosAsignados.map((u: any) => u.id));
                this.todosLosUsuarios = result.objeto.filter(
                    (usuario: any) => !assignedUsuariosIds.has(usuario.id)
                )
                // Ordenamos primero por apellidos y luego por nombres, ambos ascendentes
                this.todosLosUsuarios = this.todosLosUsuarios.sort((a: any, b: any) => {
                    const apellidoCompare = a.apellidos.toUpperCase().localeCompare(b.apellidos.toUpperCase());
                    if (apellidoCompare !== 0) {
                        return apellidoCompare;
                    }
                    return a.nombres.toUpperCase().localeCompare(b.nombres.toUpperCase());
                });

                this.usuariosAsignados = this.usuariosAsignados.sort((a: any, b: any) => {
                    const apellidoCompare = a.apellidos.toUpperCase().localeCompare(b.apellidos.toUpperCase());
                    if (apellidoCompare !== 0) {
                        return apellidoCompare;
                    }
                    return a.nombres.toUpperCase().localeCompare(b.nombres.toUpperCase());
                });

            }
        } catch (error) {
            this.notificationService.showWarning('Error al cargar los usuarios disponibles');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    clearUsuarios() {
        this.usuariosAsignados = this.role.usuarios;
        this.loadUsuariosDisponibles();
        this.selectedTabIndex = 4;
    }

    onUsuarioMoveToSource(event: any) {
        // Asumimos que 'restrictedUserLogins' son los logins de los usuarios que no se pueden quitar para el rol ADMIN.
        // Usaremos 'item.login' para la comparación y 'item.apellidos + ', ' + item.nombres' para el mensaje.
        const preventedItems = this.handleRestrictedUsuariosMove(event.items, ['LOGIN_ADMIN_1', 'LOGIN_ADMIN_2']);

        if (preventedItems.length > 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, los usuarios: ${preventedItems.join(', ')} no se pueden quitar.`);
        } else if (preventedItems.length === 1) {
            this.notificationService.showWarning(`Para el rol ADMINISTRADOR, el usuario: ${preventedItems[0]} no se puede quitar.`);
        }
    }

    async saveUsuarios() {

        this.loading = true;
        try {
            // Mapea los usuariosAsignados para obtener solo sus IDs
            const todosLosIdsAsignados = this.usuariosAsignados.map((u: any) => ({ id: u.id }));

            // Crea el objeto request con la estructura deseada
            const requestData = {
                usuarios: todosLosIdsAsignados
            };

            await this.usuarioService.updateByRole(this.role.id, requestData);
            this.notificationService.show('Se ha guardado el registro.');
            this.loadRole(this.role.id);
            this.clearUsuarios();
        } catch (error) {
            this.notificationService.showWarning('Error al guardar el registro');
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    private handleRestrictedUsuariosMove(movedItems: any[], restrictedUserLogins: string[]): string[] {
        const preventedUserFullNames: string[] = [];

        for (const item of movedItems) {
            // Moviendo de 'usuariosAsignados' (Target) a 'todosLosUsuarios' (Source)
            // Restricción: "Para rol ADMINISTRADOR no se pueden quitar los usuarios restringidos"
            // Usamos item.login para la comparación.
            if (this.role.id === 1 && restrictedUserLogins.includes(item.login)) {
                preventedUserFullNames.push(`${item.apellidos}, ${item.nombres}`);
                // Revertir el movimiento
                this.todosLosUsuarios = this.todosLosUsuarios.filter((u: { id: any; }) => u.id !== item.id);
                if (!this.usuariosAsignados.find((u: { id: any; }) => u.id === item.id)) {
                    this.usuariosAsignados.push(item);
                }
            }
        }
        // Opcional: Re-ordenar arrays
        /*
        const sortUsers = (a: any, b: any) => {
          const apellidoCompare = a.apellidos.toUpperCase().localeCompare(b.apellidos.toUpperCase());
          if (apellidoCompare !== 0) return apellidoCompare;
          return a.nombres.toUpperCase().localeCompare(b.nombres.toUpperCase());
        };
        this.todosLosUsuarios.sort(sortUsers);
        this.usuariosAsignados.sort(sortUsers);
        */
        return preventedUserFullNames;
    }
    // ******** FIN USUARIOS ********



}