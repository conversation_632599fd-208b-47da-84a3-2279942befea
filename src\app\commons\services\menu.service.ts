import { Injectable } from "@angular/core";
import { Menu } from "../models/interfaces/auth.response";

import { Response } from '../models/interfaces/response';
import { MenuDataModel } from "../models/menu-data.model";
import { BaseService } from "./base.service";
import { RestRetryService } from "./rest-retry.service";
import { RestService } from "./rest.service";
import { StorageService } from "./storage.service";

@Injectable()
export class MenuService extends BaseService {

  readonly NowIndex: string = 'storage.data.time.now';
  readonly TimeLoggerUserIndex: string = 'storage.data.identity.session';
  readonly HistoryDateIndex: string = 'storage.data.history.date';
  readonly SessionTokenIndex: string = 'storage.data.token.session';
  readonly UserLoggedIndex: string = 'storage.data.user.logged';
  readonly RoleIndex: string = 'storage.data.role';

  constructor(
    private storageService: StorageService,
    private restService: RestService,
    private restRetryService: RestRetryService
  ) {
    super();
  }

  menuPermitido(): Menu[] {
    const { menues } = this.storageService.getMenuAutorizados();
    return menues;
  }

  getUrlEntorno(): string {
    const userLogged = this.storageService.getObject(this.UserLoggedIndex) as any;

    if (!userLogged?.id) {
      return '';
    }

    return userLogged.urlEntorno;
  }

  getRutaDesdeEstado(estado: string): string {
    const userLogged = this.storageService.getObject(this.UserLoggedIndex) as any;

    if (!userLogged?.id) {
      return estado.split('.').join('/');
    }

    let ruta = estado.split('.').join('/');
    let menu: any;
    let childMenu: any;
    for (menu of userLogged.menues) {
      childMenu = menu.childs.find((row: any) => row.estado === estado);

      if (!childMenu) {
        continue;
      }

      ruta = childMenu.nuevaRuta;
      break;
    }

    return ruta;
  }

  /*   async getAllLegacy(): Promise<Response<MenuDataModel>> {
  
      try {
        const collection = await this.restService.get<Response<MenuDataModel>>(`servicio-persistencia/api/v1/app/menus/listar-menus-legacy`);
  
        return this.ok(collection);
      }
      catch (error: any) {
        return this.error(error);
      }
    } */

  async getAllLegacy(): Promise<Response<MenuDataModel>> {

    try {
      const collection = await this.restRetryService.requestWithCustomRetry<Response<MenuDataModel>>({
        method: 'get',
        endpoint: `servicio-persistencia/api/v1/app/menus/listar-menus-legacy`,
        maxRetries: 3,  // Más reintentos que el valor predeterminado
        retryDelay: 500 // Menor tiempo entre intentos
      });

      // const collection = await this.restService.get<Response<MenuDataModel>>(`servicio-persistencia/api/v1/app/menus/listar-menus-legacy`);

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }
  }

  async updateByRole(rolId: number, menus: any): Promise<Response<MenuDataModel>> {
    try {
      const collection = await this.restService.put<Response<MenuDataModel>>(`servicio-persistencia/api/v1/app/menus/modificar-por-rolId/${rolId}`, menus);

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }

  }

  async getById(menuId: number) {
    try {
      const collection = await this.restService.get<Response<MenuDataModel>>(`servicio-persistencia/api/v1/app/menus/obtenerPorId/${menuId}`);

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }
  }
}

