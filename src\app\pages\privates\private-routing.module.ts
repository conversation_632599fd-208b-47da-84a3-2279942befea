import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Constantes } from 'src/app/commons/utils/constants';
import { LayoutComponent } from './layout/layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: Constantes['dashboard.reportes.asientosContables'],
        loadChildren: () =>
          import('./reportes/asiento-contable/asiento-contable.module').then(
            (module) => module.AsientoContableModule
          ),
        data: { title: 'Asientos contables' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Inicio' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Liquidaciones' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Presupuestos' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Administración' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Legajos' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Reportes' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Ganancias' },
      },
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then(
            (module) => module.DashboardModule
          ),
        data: { title: 'Seguridad' },
      },
      {
        path: Constantes['dashboard.reportes.remuneraciones-afip'],
        loadChildren: () =>
          import(
            './reportes/remuneraciones-afip/remuneraciones-afip.module'
          ).then((module) => module.RemuneracionesAfipModule),
        data: { title: 'Remuneraciones de AFIP' },
      },
      {
        path: Constantes['dashboard.presupuestos.organigrama'],
        loadChildren: () =>
          import('./pre-ente/pre-ente.module').then(
            (module) => module.PreEnteModule
          ),
        data: { title: 'Organigrama' },
      },
      {
        path: Constantes['dashboard.usuarios'],
        loadChildren: () =>
          import('./user/user.module').then((module) => module.UserModule),
        data: { title: 'Usuarios' },
      },
      {
        path: Constantes['dashboard.incentivoDocente.excel'],
        loadChildren: () =>
          import(
            './administracion/incentivo-docente-excel/incentivo-docente-excel.module'
          ).then((module) => module.IncentivoDocenteExcelModule),
        data: { title: 'Incentivo Docente Excel' },
      },
      {
        path: Constantes['dashboard.reportes.listadoConceptos'],
        loadChildren: () =>
          import('./reportes/listado-conceptos/listado-conceptos.module').then(
            (module) => module.ListadoConceptosModule
          ),
        data: { title: 'Listado de Conceptos' },
      },
      {
        path: Constantes['dashboard.reportes.recibos'],
        loadChildren: () =>
          import('./reportes/recibos/recibos.module').then(
            (module) => module.RecibosModule
          ),
        data: { title: 'Exportación Recibos' },
      },
      {
        path: Constantes['dashboard.legajos.consulta'],
        loadChildren: () =>
          import('./consulta-legajos/consulta-legajos.module').then(
            (module) => module.ConsultaLegajosModule
          ),
        data: { title: 'Consulta de Legajos' },
      },
      {
        path: Constantes['dashboard.gananciasReportes.reportes'],
        loadChildren: () =>
          import('./ganancias/reporte-ganancia/reporte-ganancia.module').then(
            (module) => module.ReporteGananciaModule
          ),
        data: { title: 'Reportes de Ganancias' },
      },
      {
        path: Constantes['dashboard.reportes.aportesOsplad'],
        loadChildren: () =>
          import('./reportes/aportes-osplad/aportes-osplad.module').then(
            (module) => module.AportesOspladModule
          ),
        data: { title: 'Aportes OSPLAD' },
      },
      // {
      //   path: Constantes['dashboard.reportes.planillasHabilitacion'],
      //   loadChildren: () =>
      //     import(
      //       './reportes/planilla-habilitacion/planilla-habilitacion.module'
      //     ).then((module) => module.PlanillaHabilitacionModule),
      //   data: { title: 'Planillas de Habilitación' },
      // },
      // {
      //   path: Constantes['dashboard.reportes.planillasDeBlp'],
      //   loadChildren: () =>
      //     import('./reportes/planillas-blp/planillas-blp.module').then(
      //       (module) => module.PlanillasBlpModule
      //     ),
      //   data: { title: 'Planillas de BLP' },
      // },
      {
        path: Constantes['dashboard.reportes.planillasArt'],
        loadChildren: () =>
          import('./reportes/planilla-art/planilla-art.module').then(
            (module) => module.PlanillaArtModule
          ),
        data: { title: 'Planillas ART' },
      },
      {
        path: Constantes['dashboard.liquidaciones.variable'],
        loadChildren: () =>
          import('./liquidaciones/variables/variables.module').then(
            (module) => module.VariablesModule
          ),
        data: { title: 'Variables' },
      },
      {
        path: Constantes['dashboard.liquidaciones.parametrosMensuales'],
        loadChildren: () =>
          import(
            './liquidaciones/parametros-mensuales/parametros-mensuales.module'
          ).then((module) => module.ParametrosMensualesModule),
        data: { title: 'Parámetros Mensuales' },
      },
      {
        path: Constantes['dashboard.liquidaciones.parametros'],
        loadChildren: () =>
          import('./liquidaciones/parametros/parametros.module').then(
            (module) => module.ParametrosModule
          ),
        data: { title: 'Parámetros' },
      },
      // {
      //   path: Constantes['dashboard.legajos.cuentasBancarias'],
      //   loadChildren: () =>
      //     import('./legajos/cuentas-bancarias/cuentas-bancarias.module').then(
      //       (module) => module.CuentasBancariasModule
      //     ),
      //   data: { title: 'Cuentas Bancarias' },
      // },
      {
        path: Constantes['dashboard.liquidaciones.codigos'],
        loadChildren: () =>
          import('./liquidaciones/codigos/codigos.module').then(
            (module) => module.CodigosModule
          ),
        data: { title: 'Códigos' },
      },
      {
        path: Constantes['dashboard.liquidaciones.setVersiones'],
        loadChildren: () =>
          import('./liquidaciones/set-versiones/set-versiones.module').then(
            (module) => module.SetVersionesModule
          ),
        data: { title: 'Set Versiones' },
      },
      {
        path: Constantes['dashboard.liquidaciones.ReglasDeCodigosModule'],
        loadChildren: () =>
          import(
            './liquidaciones/reglas-de-codigos/reglas-de-codigos.module'
          ).then((module) => module.ReglasDeCodigosModule),
        data: { title: 'Reglas de Códigos' },
      },
      {
        path: Constantes['dashboard.ganancias.acumuladoresDeGanancias'],
        loadChildren: () =>
          import(
            './ganancias/acumuladores-de-ganancias/acumuladores-de-ganancias.module'
          ).then((module) => module.AcumuladoresDeGananciasModule),
        data: { title: 'Acumuladores de Ganancias' },
      },
      {
        path: Constantes['dashboard.reportes.planillaEmbargoyCoutaAlimentaria'],
        loadChildren: () =>
          import('./reportes/planilla-embargo/planilla-embargo.module').then(
            (module) => module.PlanillaEmbargoModule
          ),
        data: { title: 'Planilla de Embargos y Cuota Alimentaria' },
      },
      {
        path: Constantes['dashboard.reportes.certificacionPrestamo'],
        loadChildren: () =>
          import(
            './reportes/certificacion-prestamo/certificacion-prestamo.module'
          ).then((module) => module.CertificacionPrestamoModule),
        data: { title: 'Certificación préstamos ISS' },
      },
      {
        path: Constantes['dashboard.reportes.certificacionSueldosBLP'],
        loadChildren: () =>
          import(
            './reportes/prestamos-bancarios-blp/prestamos-bancarios-blp.module'
          ).then((module) => module.PrestamoBancarioBLPModule),
        data: { title: 'Certificación de Sueldos BLP' },
      },
      {
        path: Constantes['dashboard.reportes.cajaComplementariaDocente'],
        loadChildren: () =>
          import(
            './reportes/caja-complementaria-docente/caja-complementaria-docente.module'
          ).then((module) => module.CajaComplementariaDocenteModule),
        data: { title: 'Caja complementaria docente' },
      },
      {
        path: Constantes['dashboard.reportes.disenioUnicoIss'],
        loadChildren: () =>
          import('./reportes/disenio-unico-iss/disenio-unico-iss.module').then(
            (module) => module.DisenioUnicoIssModule
          ),
        data: { title: 'Diseño único ISS' },
      },
      {
        path: Constantes['dashboard.reportes.planillasTesoreria'],
        loadChildren: () =>
          import(
            './reportes/planillas-tesoreria/planillas-tesoreria.module'
          ).then((module) => module.planillasTesoreriaModule),
        data: { title: 'Planillas de tesorería' },
      },
      {
        path: Constantes['dashboard.liquidaciones.procesos'],
        loadChildren: () =>
          import('./liquidaciones/procesos/procesos.module').then(
            (module) => module.ProcesosModule
          ),
        data: { title: 'Procesos' },
      },
      {
        path: Constantes['dashboard.reportes.rendicion'],
        loadChildren: () =>
          import('./reportes/rendicion/rendicion.module').then(
            (module) => module.RendicionModule
          ),
        data: { title: 'Rendición APE' },
      },
      {
        path: Constantes['dashboard.reportes.vialidad'],
        loadChildren: () =>
          import('./reportes/vialidad/vialidad.module').then(
            (module) => module.VialidadModule
          ),
        data: { title: 'Reportes Vialidad' },
      },
      {
        path: Constantes['dashboard.reportes.ocupacionesLiquidadas'],
        loadChildren: () =>
          import(
            './reportes/ocupaciones-liquidadas/ocupaciones-liquidadas.module'
          ).then((module) => module.OcupacionesLiquidadasModule),
        data: { title: 'Tabulado de Ocupaciones liquidadas' },
      },
      {
        path: Constantes['dashboard.reportes.salidasAnses'],
        loadChildren: () =>
          import('./reportes/salidas-anses/salidas-anses.module').then(
            (module) => module.SalidasAnsesModule
          ),
        data: { title: 'Salidas Anses' },
      },
      {
        path: Constantes['dashboard.reportes.sistemasExternos'],
        loadChildren: () =>
          import('./reportes/sistemas-externos/sistemas-externos.module').then(
            (module) => module.SistemasExternosModule
          ),
        data: { title: 'Sistemas Externos' },
      },
      {
        path: Constantes['dashboard.reportes.estadisticasDocentes'],
        loadChildren: () =>
          import(
            './reportes/estadisticas-docentes/estadisticas-docentes.module'
          ).then((module) => module.EstadisticasDocentesModule),
        data: { title: 'Estadísticas Docentes' },
      },
      {
        path: Constantes['dashboard.roles'],
        loadChildren: () =>
          import('./roles/roles.module').then((module) => module.RolesModule),
        data: { title: 'Roles' },
      },
      {
        path: Constantes['dashboard.menus'],
        loadChildren: () =>
          import('./menus/menus.module').then((module) => module.MenusModule),
        data: { title: 'Menús' },
      },
      {
        path: Constantes['dashboard.acciones'],
        loadChildren: () =>
          import('./acciones/acciones.module').then(
            (module) => module.AccionesModule
          ),
        data: { title: 'Acciones' },
      },
      {
        path: Constantes['dashboard.reportes.controlesliquidacion'],
        loadChildren: () =>
          import(
            './reportes/controles-liquidacion/controles-liquidacion.module'
          ).then((module) => module.ControlesLiquidacionModule),
        data: { title: 'Controles de liquidación' },
      },
      {
        path: Constantes['dashboard.auditorias'],
        loadChildren: () =>
          import('./auditorias/auditorias.module').then(
            (module) => module.AditoriasModule
          ),
        data: { title: 'Auditorias' },
      },
    ],
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PrivateRoutingModule { }
