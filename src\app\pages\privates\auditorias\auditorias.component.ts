import { AfterViewInit, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LazyLoadEvent } from 'primeng/api/lazyloadevent';
import { UserLogModel } from 'src/app/commons/models/user-log.model';
import { EventService } from 'src/app/commons/services/event.service';
import { NotificationService } from 'src/app/commons/services/notification.service';
import { UserLogsService } from 'src/app/commons/services/userlogs.service';
import { BaseComponent } from '../components/base.component';

@Component({
  selector: 'app-auditorias',
  templateUrl: './auditorias.component.html',
  styleUrls: ['./auditorias.component.scss']
})
export class AuditoriasComponent extends BaseComponent implements OnInit, AfterViewInit {

  userLogs: any;
  selectedTabIndex: number = 0;
  filtersForm: FormGroup;
  loginControl = new FormControl('', Validators.required);
  tipoControl = new FormControl('', Validators.required);
  loading: boolean = true;
  totalRecords: number = 0;

  constructor(
    private userlogsService: UserLogsService,
    notificationService: NotificationService,
    eventService: EventService,
    private router: Router
  ) {
    super(notificationService, eventService);
    this.userLogs = new Array<UserLogModel>();
    this.filtersForm = new FormGroup({
      nombre: this.loginControl,
      nota: this.tipoControl
    });
  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  btnNuevoUserLog() {
    this.router.navigate(['/admin/auditorias/nueva']);
  }

  btnVerUserLog(userLog: any): void {
    this.router.navigate(['/admin/auditorias/ver', userLog.id]);

  }

  btnEditarUserLog(userLog: any): void {
    this.router.navigate(['/admin/auditorias/editar', userLog.id]);

  }
  btnEliminarUserLog(userLog: any): void {
    this.router.navigate(['/admin/auditorias/eliminar', userLog.id]);
  }

  async formSubmitEventHandler(): Promise<void> {
    this.loading = true;
    await this.fetchUserLogs(0);
    // No desactivamos el loading aquí porque ya se maneja en fetchUserLogs en caso de error
    if (this.loading) this.loading = false; // Solo desactivamos si aún está activo
  }

  clearFilters() {
    this.filtersForm.reset();
    // recargar los datos después de limpiar los filtros
    this.formSubmitEventHandler();
  }

  async loadUserLogs($event: LazyLoadEvent) {
    const page = $event.first! / $event.rows!;
    this.loading = true;
    await this.fetchUserLogs(page);
    // No desactivamos el loading aquí porque ya se maneja en fetchUserLogs en caso de error
    if (this.loading) this.loading = false; // Solo desactivamos si aún está activo
  }

  private async fetchUserLogs(page: number) {
    try {
      const data = {
        "login": this.loginControl.value,
        "tipo": this.tipoControl.value,
        "size": 20,
        "page": page,
        "sortBy": "creado",
        "asc": false
      };

      const result = await this.userlogsService.getAll(data);
      this.userLogs = result.content;
      this.totalRecords = result.totalElements;
    } catch (error: unknown) {
      // Desactivar el loading
      this.loading = false;

      // Limpiar los datos de usuarios
      this.userLogs = [];
      this.totalRecords = 0;

      if (error instanceof Error) {
        // Verificar si es un error de permisos (contiene {{alert}})
        if (error.message && error.message.includes('{{alert}}')) {
          const cleanMessage = error.message.replace('{{alert}}', '').trim();
          try {
            // Intentar parsear el JSON después de eliminar {{alert}}
            const errorData = JSON.parse(cleanMessage);
            let errorMessage = `Acceso denegado: ${errorData.message}`;

            this.showError(errorMessage);
          } catch (jsonError) {
            // Si hay un error al parsear el JSON, mostrar el mensaje sin formato
            this.showError(cleanMessage);
          }
          return;
        }

        // Para cualquier otro tipo de error, mostrar el mensaje directamente
        this.showError(error.message || 'Error al cargar los logs de usuarios');
      } else {
        // Es otro tipo de error
        this.showError('Error desconocido al cargar los logs de usuarios');
      }
    }
  }

  btnVolver() {
    window.history.back();
  }
}