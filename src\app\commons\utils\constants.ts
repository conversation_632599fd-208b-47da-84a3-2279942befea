interface Constante {
  [key: string]: string;
}

export const Constantes: Constante = {
  'dashboard.presupuestos.organigrama': 'pre-ente',
  'dashboard.usuarios': 'users',
  'dashboard.incentivoDocente.excel': 'incentivo',
  'dashboard.reportes.listadoConceptos': 'listado-conceptos',
  'dashboard.reportes.recibos': 'recibos',
  'dashboard.legajos.consulta': 'consulta-legajos',
  'dashboard.gananciasReportes.reportes': 'reporte-ganancia',
  'dashboard.reportes.remuneraciones-afip': 'remuneraciones-afip',
  'dashboard.reportes.asientosContables': 'asientosContables',
  'dashboard.reportes.aportesOsplad': 'aportes-osplad',
  // 'dashboard.reportes.planillasDeTesoreria': 'planillas-tesoreria',
  // 'dashboard.reportes.planillasHabilitacion': 'planilla-habilitacion',
  // 'dashboard.reportes.planillasDeBlp': 'planillas-blp',
  'dashboard.reportes.planillasArt': 'planilla-art',
  'dashboard.liquidaciones.variable': 'variables',
  'dashboard.liquidaciones.parametrosMensuales': 'parametros-mensuales',
  'dashboard.liquidaciones.parametros': 'parametros',
  // 'dashboard.legajos.cuentasBancarias': 'cuentas-bancarias',
  'dashboard.liquidaciones.codigos': 'codigos',
  'dashboard.liquidaciones.setVersiones': 'set-versiones',
  'dashboard.liquidaciones.ReglasDeCodigosModule': 'reglas-de-codigos',
  'dashboard.ganancias.acumuladoresDeGanancias': 'acumuladores-de-ganancias',
  'dashboard.reportes.planillaEmbargoyCoutaAlimentaria': 'planilla-embargo',
  'dashboard.reportes.certificacionPrestamo': 'certificacion-prestamo',
  'dashboard.reportes.certificacionSueldosBLP': 'certificacionSueldosBLP',
  'dashboard.reportes.cajaComplementariaDocente': 'cajaComplementariaDocente',
  'dashboard.reportes.disenioUnicoIss': 'disenioUnicoIss',
  'dashboard.reportes.planillasTesoreria': 'planillasTesoreria',
  'dashboard.liquidaciones.procesos': 'procesos',
  'dashboard.reportes.rendicion': 'rendicion',
  'dashboard.reportes.vialidad': 'vialidad',
  'dashboard.reportes.salidasAnses': 'salidas',
  'dashboard.reportes.ocupacionesLiquidadas': 'ocupacionesLiquidadas',
  'dashboard.reportes.sistemasExternos': 'sistemasExternos',
  'dashboard.reportes.estadisticasDocentes': 'estadisticasDocentes',
  'dashboard.roles': 'roles',
  'dashboard.menus': 'menus',
  'dashboard.acciones': 'acciones',
  'dashboard.reportes.controlesliquidacion': 'controlesliquidacion',
  'dashboard.auditorias': 'auditorias',
};
