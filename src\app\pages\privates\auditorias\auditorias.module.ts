import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { SpinnerModule } from 'src/app/commons/components/spinner/spinner.module';
import { EventService } from 'src/app/commons/services/event.service';
import { NotificationService } from 'src/app/commons/services/notification.service';
import { UserLogsService } from 'src/app/commons/services/userlogs.service';
import { AuditoriasComponent } from './auditorias.component';


// Definir las rutas del módulo
const routes: Routes = [
  // Rutas para cuando se accede desde 'dashboard.menus'
  {
    path: '',
    component: AuditoriasComponent,
  }/* ,
  {
    path: 'ver/:id',
    component: AccionVerComponent,
  },
  {
    path: 'editar/:id',
    component: AccionEditComponent,
  },
  {
    path: 'nueva',
    component: AccionEditComponent,
  }, */

];

@NgModule({
  declarations: [
    AuditoriasComponent,
    // AccionVerComponent,
    // AccionEditComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    TableModule,
    SpinnerModule,
    TabViewModule,
  ],
  providers: [
    NotificationService,
    EventService,
    UserLogsService
  ],
})
export class AditoriasModule { }
