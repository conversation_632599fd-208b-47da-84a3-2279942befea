import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { ButtonModule } from 'primeng/button';
import { PickListModule } from 'primeng/picklist';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { SpinnerModule } from 'src/app/commons/components/spinner/spinner.module';
import { AccionService } from 'src/app/commons/services/accion.service';
import { EmpresaService } from 'src/app/commons/services/empresa.service';
import { EventService } from 'src/app/commons/services/event.service';
import { MenuService } from 'src/app/commons/services/menu.service';
import { NotificationService } from 'src/app/commons/services/notification.service';
import { RoleService } from 'src/app/commons/services/role.service';
import { UserService } from 'src/app/commons/services/user.service';
import { MenuEditComponent } from './editar/menu-edit.component';
import { MenusComponent } from './menus.component';
import { MenuVerComponent } from './ver/menu-ver.component';

// Definir las rutas del módulo
const routes: Routes = [
  // Rutas para cuando se accede desde 'dashboard.menus'
  {
    path: '',
    component: MenusComponent,
  },
  {
    path: 'ver/:id',
    component: MenuVerComponent,
  },
  {
    path: 'editar/:id',
    component: MenuEditComponent,
  }
];

@NgModule({
  declarations: [
    MenusComponent,
    MenuVerComponent,
    MenuEditComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    TableModule,
    ButtonModule,
    SpinnerModule,
    TabViewModule,
    PickListModule
  ],
  providers: [
    NotificationService,
    EventService,
    MenuService,
    RoleService,
    AccionService,
    EmpresaService,
    UserService
  ],
})
export class MenusModule { }
