<section class="content">
    <app-spinner message="Cargando..." [toggle]="loading"></app-spinner>
    <div class="container-fluid" *ngIf="!loading">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h4 class="card-title"><strong>Detalles del Rol: </strong> {{role.nombre}}</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col">
                                <span><strong>ID:</strong> {{role.id}} <strong>Descripción:</strong>
                                    {{role.descripcion}}</span>
                            </div>
                        </div>
                        <p-tabView>

                            <p-tabPanel header="Permisos NNSL">
                                <span>Total de permisos asignados con rol {{ role.nombre }}: {{ permisos?.length
                                    }}</span>
                                <p-table class="table table-bordered table-striped" #dt1 [value]="permisos" dataKey="id"
                                    [paginator]="true" [rows]="20" [rowsPerPageOptions]="[5, 10, 20]" sortField="nombre"
                                    [sortOrder]="1" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
                                    [tableStyle]="{ 'min-width': '50rem' }"
                                    currentPageReportTemplate="Listando de {first} a {last} de {totalRecords} registros"
                                    [loading]="loading" [globalFilterFields]="['id', 'nombre', 'nota']">
                                    <ng-template pTemplate="caption">
                                        <div class="d-flex justify-content-start align-items-center">
                                            <button pButton label="Limpiar" class="p-button-outlined"
                                                icon="pi pi-filter-slash" (click)="clear(dt1)"></button>
                                            <div class="p-input-icon-right">
                                                <i class="pi pi-search"></i>
                                                <input type="text" pInputText class="rounded-input"
                                                    (input)="dt1.filterGlobal($any($event.target).value, 'contains')"
                                                    placeholder="Buscar..." />
                                            </div>
                                        </div>
                                    </ng-template>
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th class="text-center" style="width:10%" pSortableColumn="id">ID
                                                <p-sortIcon field="id"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="nombre">Nombre
                                                <p-sortIcon field="nombre"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="nota">Nota
                                                <p-sortIcon field="nota"></p-sortIcon>
                                            </th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-permiso>
                                        <tr>
                                            <td class="text-center">{{ permiso.id }}</td>
                                            <td class="text-left">{{ permiso.nombre }}</td>
                                            <td class="text-left">{{ permiso.nota }}</td>
                                        </tr>
                                    </ng-template>
                                </p-table>

                            </p-tabPanel>

                            <p-tabPanel header="Acciones Legacy">
                                <span>Total de acciones disponibles con rol {{ role.nombre }}: {{ acciones?.length
                                    }}</span>
                                <p-table class="table table-bordered table-striped" #dt2 [value]="acciones" dataKey="id"
                                    [paginator]="true" [rows]="20" [rowsPerPageOptions]="[5, 10, 20, 30, 40, 50]"
                                    sortField="nombre" [sortOrder]="1" [totalRecords]="totalRecords"
                                    [showCurrentPageReport]="true" [tableStyle]="{ 'min-width': '50rem' }"
                                    currentPageReportTemplate="Listando de {first} a {last} de {totalRecords} registros"
                                    [loading]="loading" [globalFilterFields]="['id','nombre','endpoint']">
                                    <ng-template pTemplate="caption">
                                        <div class="d-flex justify-content-start align-items-center">
                                            <button pButton label="Limpiar" class="p-button-outlined"
                                                icon="pi pi-filter-slash" (click)="clear(dt2)"></button>
                                            <div class="p-input-icon-right">
                                                <i class="pi pi-search"></i>
                                                <input type="text" pInputText class="rounded-input"
                                                    (input)="dt2.filterGlobal($any($event.target).value, 'contains')"
                                                    placeholder="Buscar..." />
                                            </div>
                                        </div>
                                    </ng-template>
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th class="text-center" style="width:10%" pSortableColumn="id">ID
                                                <p-sortIcon field="id"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="nombre">Nombre
                                                <p-sortIcon field="nombre"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="endpoint">Endpoint
                                                <p-sortIcon field="endpoint"></p-sortIcon>
                                            </th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-accion>
                                        <tr>
                                            <td class="text-center">{{ accion.id }}</td>
                                            <td class="text-left">{{ accion.nombre }}</td>
                                            <td class="text-left">{{ accion.endpoint }}</td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                            </p-tabPanel>

                            <p-tabPanel header="Menus">
                                <span>Total de entradas de menú para el rol {{ role.nombre }}: {{ role.menues?.length
                                    }}</span>
                                <p-table class="table table-bordered table-striped" #dt3 [value]="role.menues"
                                    dataKey="id" [paginator]="true" [rows]="20" [rowsPerPageOptions]="[5, 10, 20]"
                                    sortField="nombre" [sortOrder]="1" [totalRecords]="totalRecords"
                                    [showCurrentPageReport]="true" [tableStyle]="{ 'min-width': '50rem' }"
                                    currentPageReportTemplate="Listando de {first} a {last} de {totalRecords} registros"
                                    [loading]="loading"
                                    [globalFilterFields]="['id','nombre','estado','menuPadreId.nombre','icono','abstracto']">
                                    <ng-template pTemplate="caption">
                                        <div class="d-flex justify-content-start align-items-center">
                                            <button pButton label="Limpiar" class="p-button-outlined"
                                                icon="pi pi-filter-slash" (click)="clear(dt3)"></button>
                                            <div class="p-input-icon-right">
                                                <i class="pi pi-search"></i>
                                                <input type="text" pInputText class="rounded-input"
                                                    (input)="dt3.filterGlobal($any($event.target).value, 'contains')"
                                                    placeholder="Buscar..." />
                                            </div>
                                        </div>
                                    </ng-template>
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th class="text-center" style="width:10%" pSortableColumn="id">ID
                                                <p-sortIcon field="id"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="nombre">Nombre
                                                <p-sortIcon field="nombre"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="estado">Estado
                                                <p-sortIcon field="estado"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%"
                                                pSortableColumn="menuPadreId.nombre">Menu Padre
                                                <p-sortIcon field="menuPadreId.nombre"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="icono">Icono
                                                <p-sortIcon field="icono"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="abstracto">
                                                Abstracto <p-sortIcon field="abstracto"></p-sortIcon>
                                            </th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-menu>
                                        <tr>
                                            <td class="text-center">{{ menu.id }}</td>
                                            <td class="text-left">{{ menu.nombre }}</td>
                                            <td class="text-left">{{ menu.estado }}</td>
                                            <td class="text-left">{{ menu.menuPadreId?.nombre }}</td>
                                            <td class="text-left"><i class="fa fa-{{ menu.icono }}"></i> {{ menu.icono
                                                }}</td>
                                            <td class="text-left">{{ menu.abstracto ? 'Sí' : 'No' }}</td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                            </p-tabPanel>

                            <p-tabPanel header="Empresas">
                                <span>Total de empresas asignadas al rol {{ role.nombre }}: {{ role.empresas?.length
                                    }}</span>
                                <p-table class="table table-bordered table-striped" #dt4 [value]="role.empresas"
                                    dataKey="id" [paginator]="true" [rows]="20" [rowsPerPageOptions]="[5, 10, 20]"
                                    sortField="descripcion" [sortOrder]="1" [totalRecords]="totalRecords"
                                    [showCurrentPageReport]="true" [tableStyle]="{ 'min-width': '50rem' }"
                                    currentPageReportTemplate="Listando de {first} a {last} de {totalRecords} registros"
                                    [loading]="loading"
                                    [globalFilterFields]="['id','descripcion','codigo','convenios']">
                                    <ng-template pTemplate="caption">
                                        <div class="d-flex justify-content-start align-items-center">
                                            <button pButton label="Limpiar" class="p-button-outlined"
                                                icon="pi pi-filter-slash" (click)="clear(dt4)"></button>
                                            <div class="p-input-icon-right">
                                                <i class="pi pi-search"></i>
                                                <input type="text" pInputText class="rounded-input"
                                                    (input)="dt4.filterGlobal($any($event.target).value, 'contains')"
                                                    placeholder="Buscar..." />
                                            </div>
                                        </div>
                                    </ng-template>
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th class="text-center" style="width:10%" pSortableColumn="id">ID
                                                <p-sortIcon field="id"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="descripcion">
                                                Descripción <p-sortIcon field="descripcion"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="codigo">Código
                                                <p-sortIcon field="codigo"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="convenios">
                                                Convenios <p-sortIcon field="convenios"></p-sortIcon>
                                            </th>
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-empresa>
                                        <tr>
                                            <td class="text-center">{{ empresa.id }}</td>
                                            <td class="text-left">{{ empresa.descripcion }}</td>
                                            <td class="text-left">{{ empresa.codigo }}</td>
                                            <td class="text-left">{{ empresa.convenios }}</td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                            </p-tabPanel>
                            <p-tabPanel header="Usuarios">
                                <span>Total de usuarios con rol {{ role.nombre }}: {{ role.usuarios?.length }}</span>
                                <p-table class="table table-bordered table-striped" #dt5 [value]="role.usuarios"
                                    dataKey="id" [paginator]="true" [rows]="20" [rowsPerPageOptions]="[5, 10, 20]"
                                    sortField="apellidos" [sortOrder]="1" [totalRecords]="totalRecords"
                                    [showCurrentPageReport]="true" [tableStyle]="{ 'min-width': '50rem' }"
                                    currentPageReportTemplate="Listando de {first} a {last} de {totalRecords} registros"
                                    [loading]="loading"
                                    [globalFilterFields]="['id','login', 'nombres', 'apellidos', 'estado', 'demo']">
                                    <ng-template pTemplate="caption">
                                        <div class="d-flex justify-content-start align-items-center">
                                            <button pButton label="Limpiar" class="p-button-outlined"
                                                icon="pi pi-filter-slash" (click)="clear(dt5)"></button>
                                            <div class="p-input-icon-right">
                                                <i class="pi pi-search"></i>
                                                <input type="text" pInputText class="rounded-input"
                                                    (input)="dt5.filterGlobal($any($event.target).value, 'contains')"
                                                    placeholder="Buscar..." />
                                            </div>
                                        </div>
                                    </ng-template>
                                    <ng-template pTemplate="header">
                                        <tr>
                                            <th class="text-center" style="width:10%" pSortableColumn="id">ID
                                                <p-sortIcon field="id"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="login">Login
                                                <p-sortIcon field="login"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="nombres">Nombres
                                                <p-sortIcon field="nombres"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="apellidos">
                                                Apellidos <p-sortIcon field="apellidos"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="estado">Estado
                                                <p-sortIcon field="estado"></p-sortIcon>
                                            </th>
                                            <th class="text-left" style="width:20%" pSortableColumn="demo">Demo
                                                <p-sortIcon field="demo"></p-sortIcon>
                                            </th>
                                            <!--  <th class="text-left" style="width:20%" pSortableColumn="enNsl">En NNSL <p-sortIcon
                          field="enNsl"></p-sortIcon>
                      </th> -->
                                        </tr>
                                    </ng-template>
                                    <ng-template pTemplate="body" let-user>
                                        <tr>
                                            <td class="text-center">{{ user.id }}</td>
                                            <td class="text-left">{{ user.login }}</td>
                                            <td class="text-left">{{ user.nombres }}</td>
                                            <td class="text-left">{{ user.apellidos }}</td>
                                            <td class="text-left">{{ user.estado }}</td>
                                            <td class="text-left">{{ user.demo }}</td>
                                            <!-- <td class="text-left">{{ user.enNsl }}</td> -->
                                        </tr>
                                    </ng-template>
                                </p-table>
                            </p-tabPanel>
                        </p-tabView>
                    </div>
                    <div class="card-footer">
                        <div class="col-md-12 d-inline-flex justify-content-end">
                            <button type="button" class="btn btn-block btn-success btn-sm w-auto mt-0 ml-1"
                                (click)="btnVolver()"><i class="fas fa-angle-left pr-1"></i>Volver</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>