<section class="content">
    <app-spinner message="Procesando..." [toggle]="loading"></app-spinner>
    <div class="container-fluid" *ngIf="!loading">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h4 class="card-title">Editar Rol: {{role.nombre}}</h4>
                    </div>

                    <form [formGroup]="editForm" autocomplete="off" (ngSubmit)="formSubmitEventHandler()">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="nombre" class="form-label">Nombre</label>
                                        <input type="text" class="form-control" id="nombre" formControlName="nombre"
                                            maxlength="{{ maxNombre }}" (keypress)="validarTecla($event)"
                                            (input)="onNombreChange($event)">
                                        <small class="d-flex justify-content-end text-muted">{{ letrasRestantesNombre }}
                                            / {{ maxNombre
                                            }} </small>
                                        <small class="text-danger"
                                            *ngIf="editForm.get('nombre')?.invalid && editForm.get('nombre')?.touched">
                                            Nombre inválido (solo letras mayúsculas, espacios, guión bajo y punto).
                                        </small>

                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="descripcion" class="form-label">Descripción</label>
                                        <input type="text" class="form-control" id="descripcion"
                                            formControlName="descripcion" maxlength="{{ maxDescripcion }}">
                                        <small class="d-flex justify-content-end text-muted">{{
                                            letrasRestantesDescripcion }} /
                                            {{ maxDescripcion }} </small>
                                        <small class="text-danger"
                                            *ngIf="editForm.get('descripcion')?.invalid && editForm.get('descripcion')?.touched">
                                            Descripción inválida (solo letras, números, espacio, punto, coma y guión
                                            bajo).
                                        </small>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer ">
                            <div class="col-md-12 d-inline-flex  justify-content-end">
                                <button type="submit" class="btn btn-block btn-success btn-sm w-auto"><i
                                        class="fas fa-save pr-1"></i>Guardar</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearForm()"><i class="fas fa-eraser pr-1"></i>Limpiar</button>
                            </div>
                        </div>
                    </form>

                    <p-tabView [(activeIndex)]="selectedTabIndex">

                        <p-tabPanel header="Permisos NNSL" leftIcon="pi pi-key">
                            <div class="col-md-12 d-inline-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-block btn-success btn-sm w-auto"
                                    (click)="savePermisos()"><i class="fas fa-save pr-1"></i>Guardar Permisos</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearPermisos()"><i class="fas fa-sync pr-1"></i>Recargar Permisos</button>
                            </div>
                            <p-pickList [source]="todosLosPermisos" [target]="permisosAsignados"
                                sourceHeader="Disponibles ({{ todosLosPermisos?.length }})"
                                targetHeader="Asignados ({{ permisosAsignados?.length }})" [dragdrop]="true"
                                [responsive]="true" (onMoveToSource)="onPermisosMoveToSource($event)"
                                (onMoveAllToSource)="onPermisosMoveToSource($event)"
                                (onMoveToTarget)="onPermisosMoveToTarget($event)"
                                (onMoveAllToTarget)="onPermisosMoveToTarget($event)" [sourceStyle]="{'height':'300px'}"
                                [targetStyle]="{'height':'300px'}" [showSourceControls]="false"
                                [showTargetControls]="false" filterBy="nombre"
                                sourceFilterPlaceholder="Buscar en disponibles..."
                                targetFilterPlaceholder="Buscar en asignados...">
                                <ng-template let-permiso pTemplate="item">
                                    <div class="p-picklist-item">
                                        <div class="p-1">
                                            <div class="flex align-items-center">
                                                <i class="pi pi-key text-sm mr-2"></i>
                                                <strong><span>{{ permiso.nombre }}</span></strong>
                                            </div>
                                            <div class="flex align-items-center text-sm">
                                                <i class="pi pi-tags text-sm mr-2"></i>
                                                <span class="mr-2">{{ permiso.nota }}</span>
                                                <span class="font-bold">{{ 'ID: ' + permiso.id }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-pickList>
                        </p-tabPanel>

                        <p-tabPanel header="Acciones Legacy" leftIcon="pi pi-directions">
                            <div class="col-md-12 d-inline-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-block btn-success btn-sm w-auto"
                                    (click)="saveAcciones()"><i class="fas fa-save pr-1"></i>Guardar Acciones</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearAcciones()"><i class="fas fa-sync pr-1"></i>Recargar Acciones</button>
                            </div>
                            <p-pickList [source]="todasLasAcciones" [target]="accionesAsignadas"
                                sourceHeader="Disponibles ({{ todasLasAcciones?.length }})"
                                targetHeader="Asignados ({{ accionesAsignadas?.length }})" [dragdrop]="true"
                                [responsive]="true" (onMoveToSource)="onAccionesMoveToSource($event)"
                                (onMoveAllToSource)="onAccionesMoveToSource($event)"
                                (onMoveToTarget)="onAccionesMoveToTarget($event)"
                                (onMoveAllToTarget)="onAccionesMoveToTarget($event)" [sourceStyle]="{'height':'300px'}"
                                [targetStyle]="{'height':'300px'}" [showSourceControls]="false"
                                [showTargetControls]="false" filterBy="nombre"
                                sourceFilterPlaceholder="Buscar en disponibles..."
                                targetFilterPlaceholder="Buscar en asignados...">
                                <ng-template let-accion pTemplate="item">
                                    <div class="p-picklist-item">
                                        <div class="p-1">
                                            <div class="flex align-items-center">
                                                <i class="pi pi-directions text-sm mr-2"></i>
                                                <strong><span>{{ accion.nombre }}</span></strong>
                                            </div>
                                            <div class="flex align-items-center text-sm">
                                                <i class="pi pi-tags text-sm mr-2"></i>
                                                <span class="mr-2">{{ accion.endpoint }}</span>
                                                <span class="font-bold">{{ 'ID: ' + accion.id }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-pickList>
                        </p-tabPanel>

                        <p-tabPanel header="Menus" leftIcon="pi pi-folder-open">
                            <div class="col-md-12 d-inline-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-block btn-success btn-sm w-auto"
                                    (click)="saveMenus()"><i class="fas fa-save pr-1"></i>Guardar Menus</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearMenus()"><i class="fas fa-sync pr-1"></i>Recargar Menus</button>
                            </div>
                            <p-pickList [source]="todosLosMenus" [target]="menusAsignados"
                                sourceHeader="Disponibles ({{ todosLosMenus?.length }})"
                                targetHeader="Asignados ({{ menusAsignados?.length }})" [dragdrop]="true"
                                [responsive]="true" (onMoveToSource)="onMenusMoveToSource($event)"
                                (onMoveAllToSource)="onMenusMoveToSource($event)" [sourceStyle]="{'height':'300px'}"
                                [targetStyle]="{'height':'300px'}" [showSourceControls]="false"
                                [showTargetControls]="false" filterBy="nombre"
                                sourceFilterPlaceholder="Buscar en disponibles..."
                                targetFilterPlaceholder="Buscar en asignados...">
                                <ng-template let-menu pTemplate="item">
                                    <div class="p-picklist-item">
                                        <div class="p-1">
                                            <div class="flex align-items-center">
                                                <i class="pi pi-folder-open text-sm mr-2"></i>
                                                <strong><span>{{ menu.nombre }}</span></strong>
                                            </div>
                                            <div class="flex align-items-center text-sm">
                                                <i class="pi pi-tags text-sm mr-2"></i>
                                                <span class="mr-2">{{ menu.estado }}</span>
                                                <span class="font-bold">{{ 'ID: ' + menu.id }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-pickList>
                        </p-tabPanel>

                        <p-tabPanel header="Empresas" leftIcon="pi pi-building">
                            <div class="col-md-12 d-inline-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-block btn-success btn-sm w-auto"
                                    (click)="saveEmpresas()"><i class="fas fa-save pr-1"></i>Guardar Empresas</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearEmpresas()"><i class="fas fa-sync pr-1"></i>Recargar Empresas</button>
                            </div>
                            <p-pickList [source]="todasLasEmpresas" [target]="empresasAsignadas"
                                sourceHeader="Disponibles ({{ todasLasEmpresas?.length }})"
                                targetHeader="Asignados ({{ empresasAsignadas?.length }})" [dragdrop]="true"
                                [responsive]="true" (onMoveToSource)="onEmpresaMoveToSource($event)"
                                (onMoveAllToSource)="onEmpresaMoveToSource($event)" [sourceStyle]="{'height':'300px'}"
                                [targetStyle]="{'height':'300px'}" [showSourceControls]="false"
                                [showTargetControls]="false" filterBy="descripcion"
                                sourceFilterPlaceholder="Buscar en disponibles..."
                                targetFilterPlaceholder="Buscar en asignados...">
                                <ng-template let-empresa pTemplate="item">
                                    <div class="p-picklist-item">
                                        <div class="p-1">
                                            <div class="flex align-items-center">
                                                <i class="pi pi-building text-sm mr-2"></i>
                                                <strong><span>{{ empresa.descripcion }}</span></strong>
                                            </div>
                                            <div class="flex align-items-center text-sm">
                                                <i class="pi pi-tags text-sm mr-2"></i>
                                                <span class="mr-2">{{ 'Código: ' + empresa.codigo }}</span>
                                                <span class="font-bold">{{ 'ID: ' + empresa.id }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-pickList>
                        </p-tabPanel>

                        <p-tabPanel header="Usuarios" leftIcon="pi pi-users">
                            <div class="col-md-12 d-inline-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-block btn-success btn-sm w-auto"
                                    (click)="saveUsuarios()"><i class="fas fa-save pr-1"></i>Guardar Usuarios</button>
                                <button type="button" class="btn btn-block btn-danger btn-sm w-auto mt-0 ml-1"
                                    (click)="clearUsuarios()"><i class="fas fa-sync pr-1"></i>Recargar Usuarios</button>
                            </div>
                            <p-pickList [source]="todosLosUsuarios" [target]="usuariosAsignados"
                                sourceHeader="Disponibles ({{ todosLosUsuarios?.length }})"
                                targetHeader="Asignados ({{ usuariosAsignados?.length }})" [dragdrop]="true"
                                [responsive]="true" (onMoveToSource)="onUsuarioMoveToSource($event)"
                                (onMoveAllToSource)="onUsuarioMoveToSource($event)" [sourceStyle]="{'height':'300px'}"
                                [targetStyle]="{'height':'300px'}" [showSourceControls]="false"
                                [showTargetControls]="false" filterBy="nombres, apellidos, login"
                                sourceFilterPlaceholder="Buscar en disponibles..."
                                targetFilterPlaceholder="Buscar en asignados...">
                                <ng-template let-usuario pTemplate="item">
                                    <div class="p-picklist-item">
                                        <div class="p-1">
                                            <div class="flex align-items-center">
                                                <i class="pi pi-user text-sm mr-2"></i>
                                                <strong><span>{{ usuario.apellidos }}, {{ usuario.nombres
                                                        }}</span></strong>
                                            </div>
                                            <div class="flex align-items-center text-sm">
                                                <i class="pi pi-tags text-sm mr-2"></i>
                                                <span class="mr-2">{{ 'Cuit: ' + usuario.login + ' Estado: ' +
                                                    usuario.estado + ' Demo: ' +
                                                    usuario.demo }}</span>
                                                <span class="font-bold">{{ 'ID: ' + usuario.id }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-pickList>
                        </p-tabPanel>

                    </p-tabView>

                    <div class="card-footer">
                        <div class="col-md-12 d-inline-flex justify-content-end">
                            <button type="button" class="btn btn-block btn-success btn-sm w-auto mt-0 ml-1"
                                (click)="btnVolver()"><i class="fas fa-angle-left pr-1"></i>Volver</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>