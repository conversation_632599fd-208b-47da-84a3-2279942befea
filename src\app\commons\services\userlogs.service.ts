import { Injectable } from '@angular/core';
import { Pagination } from '../models/interfaces/pagination';
import { Response } from '../models/interfaces/response';
import { UserDataModel } from '../models/user-data.model';
import { UserLogModel } from '../models/user-log.model';
import { BaseService } from './base.service';
import { RestService } from './rest.service';


@Injectable()
export class UserLogsService extends BaseService {

  constructor(
    private restService: RestService,
  ) {
    super();
  }

  async getAll(data: any): Promise<Pagination<UserLogModel>> {
    try {
      const collection = await this.restService.post<Pagination<UserLogModel>>(`servicio-persistencia/api/v1/app/usuarios-logs/listar`, data);

      const response = new Array<UserLogModel>();
      for (const userLog of collection.content) {
        response.push(toUserLogModel(userLog));
      }

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }
  }

  async getAllFast(): Promise<Response<UserDataModel>> {
    try {
      const collection = await this.restService.get<Response<UserDataModel>>(`servicio-persistencia/api/v1/app/usuarios/listar-rapido`);

      const response = toUserModel(collection.objeto);

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }
  }

  async updateByRole(rolId: number, usuarios: any): Promise<Response<UserDataModel>> {
    try {
      const collection = await this.restService.put<Response<UserDataModel>>(`servicio-persistencia/api/v1/app/usuarios/modificar-por-rolId/${rolId}`, usuarios);

      return this.ok(collection);
    }
    catch (error: any) {
      return this.error(error);
    }

  }

}

export const toUserLogModel = (userLog: UserLogModel): UserLogModel => {
  const model = new UserLogModel();
  model.id = userLog.id;
  model.login = userLog.login;
  model.estado = userLog.estado;
  model.tipo = userLog.tipo;
  model.resultado = userLog.resultado;
  model.role = userLog.role;
  if (model.creado) {
    model.creado = userLog.creado.split("T")[0];
  }
  return model;
};

export const toUserModel = (user: any): UserDataModel => {
  const model = new UserDataModel();
  model.id = user.id;
  model.login = user.login;
  model.nombres = user.nombres;
  model.apellidos = user.apellidos;
  model.demo = user.demo;
  model.enNsl = user.enNsl;

  return model;
};
